#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页转Markdown示例脚本
演示如何使用WebPageToMarkdown类
"""

from webpage_to_markdown import WebPageToMarkdown

def main():
    # 创建转换器实例
    converter = WebPageToMarkdown()
    
    # 示例URL列表
    urls = [
        "https://mp.weixin.qq.com/s/Q0gy_Tf2ZMVlvMd8A8v4ag",  # 微信公众号文章
        # 可以添加更多URL
    ]
    
    print("开始批量转换网页...")
    
    success_count = 0
    for i, url in enumerate(urls, 1):
        print(f"\n[{i}/{len(urls)}] 处理: {url}")
        
        try:
            result = converter.convert_url(url)
            if result:
                success_count += 1
                print(f"✅ 成功转换，文件保存至: {result}")
            else:
                print("❌ 转换失败")
        except Exception as e:
            print(f"❌ 转换出错: {e}")
    
    print(f"\n转换完成！成功: {success_count}/{len(urls)}")

if __name__ == "__main__":
    main()
