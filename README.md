# 网页内容提取转Markdown工具

这是一个Python脚本，可以提取网页的正文内容并转换为Markdown格式。特别针对微信公众号等网站进行了优化。

## 功能特点

- 🌐 支持各种网站的内容提取
- 📱 特别优化微信公众号文章
- 🖼️ 自动将图片转换为Markdown链接格式
- 🧹 智能清理无关内容（导航、广告等）
- ⚙️ 可配置的内容选择器
- 📁 批量处理多个URL
- 🔄 自动重试机制
- 📝 保留文章标题和元数据

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

```bash
# 快速演示（推荐新手使用）
python quick_start.py
```

## 使用方法

### 1. 基本用法

```bash
# 转换单个网页
python webpage_to_markdown.py "https://mp.weixin.qq.com/s/Q0gy_Tf2ZMVlvMd8A8v4ag"

# 交互式输入
python webpage_to_markdown.py
```

### 2. 指定输出目录

```bash
python webpage_to_markdown.py -o ./my_output "https://example.com/article"
```

### 3. 使用配置文件

```bash
python webpage_to_markdown.py -c config.json "https://example.com/article"
```

### 4. 批量处理

创建一个包含URL列表的文件 `urls.txt`：
```
https://mp.weixin.qq.com/s/Q0gy_Tf2ZMVlvMd8A8v4ag
https://example.com/article1
https://example.com/article2
```

然后运行：
```bash
python webpage_to_markdown.py -f urls.txt
```

### 5. 编程方式使用

```python
from webpage_to_markdown import WebPageToMarkdown

# 创建转换器
converter = WebPageToMarkdown()

# 转换单个URL
result = converter.convert_url("https://mp.weixin.qq.com/s/Q0gy_Tf2ZMVlvMd8A8v4ag")
if result:
    print(f"转换成功，文件保存至: {result}")
```

### 6. 运行测试

```bash
# 测试不同网站的转换效果
python test_websites.py

# 运行示例脚本
python example.py
```

## 配置文件说明

`config.json` 文件可以自定义以下选项：

```json
{
  "output_dir": "./output",           // 输出目录
  "timeout": 30,                      // 请求超时时间（秒）
  "retry_times": 3,                   // 重试次数
  "delay": 1,                         // 重试间隔（秒）
  "remove_tags": [                    // 要移除的HTML标签
    "script", "style", "nav", "footer", "header", "aside"
  ],
  "content_selectors": [              // 内容选择器（按优先级排序）
    ".rich_media_content",            // 微信公众号
    "article",
    ".article-content",
    ".content",
    ".post-content",
    ".entry-content",
    "main",
    "#content"
  ]
}
```

## 输出格式

生成的Markdown文件包含：

1. **文章标题**
2. **元数据**：原文链接、提取时间
3. **正文内容**：转换为Markdown格式
4. **图片**：转换为Markdown图片链接

示例输出：
```markdown
# 文章标题

**原文链接**: https://example.com/article
**提取时间**: 2025-07-02 10:30:00

---

这里是文章正文内容...

![图片描述](https://example.com/image.jpg)

更多内容...
```

## 支持的网站

- ✅ 微信公众号文章
- ✅ 知乎文章
- ✅ 简书文章
- ✅ CSDN博客
- ✅ 博客园
- ✅ 掘金文章
- ✅ 大部分新闻网站
- ✅ 个人博客

## 注意事项

1. 某些网站可能有反爬虫机制，如果遇到问题可以：
   - 增加重试次数和延迟时间
   - 修改User-Agent
   - 使用代理

2. 对于动态加载的内容，此工具可能无法获取完整内容

3. 图片链接可能存在防盗链，建议下载到本地

## 故障排除

### 常见问题

1. **网络连接失败**
   - 检查网络连接
   - 尝试使用代理
   - 增加超时时间

2. **内容提取不完整**
   - 检查网站是否使用JavaScript动态加载
   - 调整content_selectors配置
   - 查看网页源码，添加合适的选择器

3. **编码问题**
   - 脚本会自动检测编码，通常不会有问题
   - 如有问题，可以手动指定编码

## 许可证

MIT License
