#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同网站的转换效果
"""

from webpage_to_markdown import WebPageToMarkdown
import time

def test_websites():
    """测试不同类型的网站"""
    
    # 测试网站列表
    test_urls = [
        {
            "name": "微信公众号",
            "url": "https://mp.weixin.qq.com/s/Q0gy_Tf2ZMVlvMd8A8v4ag",
            "description": "技术文章，包含图片和代码"
        },
        # 可以添加更多测试网站
        # {
        #     "name": "知乎文章",
        #     "url": "https://zhuanlan.zhihu.com/p/xxx",
        #     "description": "知乎专栏文章"
        # },
        # {
        #     "name": "CSDN博客",
        #     "url": "https://blog.csdn.net/xxx/article/details/xxx",
        #     "description": "技术博客"
        # }
    ]
    
    converter = WebPageToMarkdown()
    
    print("🚀 开始测试不同网站的转换效果...\n")
    
    results = []
    
    for i, site in enumerate(test_urls, 1):
        print(f"[{i}/{len(test_urls)}] 测试 {site['name']}")
        print(f"URL: {site['url']}")
        print(f"描述: {site['description']}")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            result = converter.convert_url(site['url'])
            end_time = time.time()
            
            if result:
                results.append({
                    "name": site['name'],
                    "status": "✅ 成功",
                    "file": result,
                    "time": f"{end_time - start_time:.2f}s"
                })
                print(f"✅ 转换成功，耗时: {end_time - start_time:.2f}s")
                print(f"📁 文件保存至: {result}")
            else:
                results.append({
                    "name": site['name'],
                    "status": "❌ 失败",
                    "file": None,
                    "time": f"{end_time - start_time:.2f}s"
                })
                print("❌ 转换失败")
                
        except Exception as e:
            end_time = time.time()
            results.append({
                "name": site['name'],
                "status": f"❌ 错误: {str(e)[:50]}...",
                "file": None,
                "time": f"{end_time - start_time:.2f}s"
            })
            print(f"❌ 转换出错: {e}")
        
        print("\n" + "="*60 + "\n")
    
    # 输出测试结果汇总
    print("📊 测试结果汇总:")
    print("-" * 60)
    for result in results:
        print(f"{result['name']:<15} {result['status']:<20} {result['time']}")
    
    success_count = sum(1 for r in results if "成功" in r['status'])
    print(f"\n总计: {success_count}/{len(results)} 个网站转换成功")

if __name__ == "__main__":
    test_websites()
