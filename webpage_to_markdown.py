#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页内容提取并转换为Markdown脚本
支持提取网页正文内容，将图片转换为链接，输出为markdown格式
"""

import requests
import json
import argparse
import os
import re
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from markdownify import markdownify as md
import time


class WebPageToMarkdown:
    def __init__(self, config_file=None):
        """初始化"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # 加载配置
        self.config = self.load_config(config_file)
        
    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "output_dir": "./output",
            "timeout": 30,
            "retry_times": 3,
            "delay": 1,
            "remove_tags": ["script", "style", "nav", "footer", "header", "aside"],
            "content_selectors": [
                ".rich_media_content",  # 微信公众号
                "article",
                ".article-content",
                ".content",
                ".post-content",
                ".entry-content",
                "main",
                "#content"
            ]
        }
        
        if config_file and os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                print(f"配置文件加载失败: {e}")
        
        return default_config
    
    def fetch_webpage(self, url):
        """获取网页内容"""
        for attempt in range(self.config['retry_times']):
            try:
                print(f"正在获取网页内容... (尝试 {attempt + 1}/{self.config['retry_times']})")
                response = self.session.get(url, timeout=self.config['timeout'])
                response.raise_for_status()
                response.encoding = response.apparent_encoding
                return response.text
            except Exception as e:
                print(f"获取失败: {e}")
                if attempt < self.config['retry_times'] - 1:
                    time.sleep(self.config['delay'])
                else:
                    raise
    
    def extract_content(self, html, url):
        """提取网页主要内容"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # 移除不需要的标签
        for tag_name in self.config['remove_tags']:
            for tag in soup.find_all(tag_name):
                tag.decompose()
        
        # 尝试找到主要内容区域
        content_element = None
        for selector in self.config['content_selectors']:
            content_element = soup.select_one(selector)
            if content_element:
                print(f"使用选择器找到内容: {selector}")
                break
        
        # 如果没有找到特定的内容区域，使用整个body
        if not content_element:
            content_element = soup.find('body') or soup
            print("使用整个页面内容")
        
        # 处理图片
        self.process_images(content_element, url)
        
        # 清理内容
        self.clean_content(content_element)
        
        return content_element
    
    def process_images(self, element, base_url):
        """处理图片，转换为markdown链接"""
        for img in element.find_all('img'):
            # 优先使用data-src（懒加载），然后是src
            src = img.get('data-src', '') or img.get('src', '')
            alt = img.get('alt', '')

            if src:
                # 转换为绝对URL
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = urljoin(base_url, src)
                elif not src.startswith('http'):
                    src = urljoin(base_url, src)

                # 创建markdown图片链接
                img_markdown = f"![{alt}]({src})"

                # 替换img标签
                img.replace_with(img_markdown)
    
    def clean_content(self, element):
        """清理内容"""
        # 移除空的段落和div
        for tag in element.find_all(['p', 'div']):
            if not tag.get_text(strip=True):
                tag.decompose()
        
        # 移除多余的属性
        for tag in element.find_all():
            # 保留一些有用的属性
            keep_attrs = ['href', 'src', 'alt', 'title']
            attrs_to_remove = []
            for attr in tag.attrs:
                if attr not in keep_attrs:
                    attrs_to_remove.append(attr)
            for attr in attrs_to_remove:
                del tag.attrs[attr]
    
    def html_to_markdown(self, element):
        """将HTML转换为Markdown"""
        html_content = str(element)

        # 使用markdownify转换
        markdown_content = md(
            html_content,
            heading_style="ATX",
            bullets="-",
            strip=['script', 'style']
        )
        
        # 清理多余的空行
        markdown_content = re.sub(r'\n\s*\n\s*\n', '\n\n', markdown_content)
        markdown_content = markdown_content.strip()
        
        return markdown_content
    
    def extract_title(self, html):
        """提取页面标题"""
        soup = BeautifulSoup(html, 'html.parser')
        
        # 尝试多种方式获取标题
        title_selectors = [
            'h1',
            '.rich_media_title',  # 微信公众号
            'title',
            '.article-title',
            '.post-title',
            '.entry-title'
        ]
        
        for selector in title_selectors:
            title_element = soup.select_one(selector)
            if title_element:
                title = title_element.get_text(strip=True)
                if title and len(title) > 5:  # 确保标题有意义
                    return title
        
        return "未知标题"
    
    def save_markdown(self, content, title, url):
        """保存markdown文件"""
        # 创建输出目录
        os.makedirs(self.config['output_dir'], exist_ok=True)
        
        # 生成文件名
        safe_title = re.sub(r'[^\w\s-]', '', title)
        safe_title = re.sub(r'[-\s]+', '-', safe_title)
        filename = f"{safe_title[:50]}.md"
        filepath = os.path.join(self.config['output_dir'], filename)
        
        # 添加元数据
        metadata = f"""# {title}

**原文链接**: {url}
**提取时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}

---

"""
        
        full_content = metadata + content
        
        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(full_content)
        
        print(f"文件已保存: {filepath}")
        return filepath
    
    def convert_url(self, url):
        """转换单个URL"""
        try:
            print(f"开始处理: {url}")
            
            # 获取网页内容
            html = self.fetch_webpage(url)
            
            # 提取标题
            title = self.extract_title(html)
            print(f"页面标题: {title}")
            
            # 提取主要内容
            content_element = self.extract_content(html, url)
            
            # 转换为markdown
            markdown_content = self.html_to_markdown(content_element)
            
            # 保存文件
            filepath = self.save_markdown(markdown_content, title, url)
            
            print(f"转换完成: {url}")
            return filepath
            
        except Exception as e:
            print(f"转换失败: {url}")
            print(f"错误信息: {e}")
            return None


def main():
    parser = argparse.ArgumentParser(description='网页内容提取并转换为Markdown')
    parser.add_argument('url', nargs='?', help='要转换的网页URL')
    parser.add_argument('-c', '--config', help='配置文件路径')
    parser.add_argument('-o', '--output', help='输出目录')
    parser.add_argument('-f', '--file', help='包含URL列表的文件')
    
    args = parser.parse_args()
    
    # 创建转换器
    converter = WebPageToMarkdown(args.config)
    
    # 如果指定了输出目录，覆盖配置
    if args.output:
        converter.config['output_dir'] = args.output
    
    urls = []
    
    # 获取URL列表
    if args.file:
        # 从文件读取URL
        try:
            with open(args.file, 'r', encoding='utf-8') as f:
                urls = [line.strip() for line in f if line.strip()]
        except Exception as e:
            print(f"读取URL文件失败: {e}")
            return
    elif args.url:
        urls = [args.url]
    else:
        # 交互式输入
        url = input("请输入要转换的网页URL: ").strip()
        if url:
            urls = [url]
    
    if not urls:
        print("没有提供有效的URL")
        return
    
    # 转换URL
    success_count = 0
    for url in urls:
        if converter.convert_url(url):
            success_count += 1
        print("-" * 50)
    
    print(f"转换完成: {success_count}/{len(urls)} 个URL成功转换")


if __name__ == "__main__":
    main()
