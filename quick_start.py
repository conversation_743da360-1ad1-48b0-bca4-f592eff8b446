#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速开始脚本
演示如何快速使用网页转Markdown工具
"""

from webpage_to_markdown import WebPageToMarkdown

def quick_demo():
    """快速演示"""
    print("🌐 网页转Markdown工具 - 快速演示")
    print("=" * 50)
    
    # 获取用户输入
    url = input("请输入要转换的网页URL（回车使用默认示例）: ").strip()
    
    # 如果用户没有输入，使用默认URL
    if not url:
        url = "https://mp.weixin.qq.com/s/Q0gy_Tf2ZMVlvMd8A8v4ag"
        print(f"使用默认URL: {url}")
    
    print("\n🚀 开始转换...")
    
    # 创建转换器
    converter = WebPageToMarkdown()
    
    try:
        # 转换URL
        result = converter.convert_url(url)
        
        if result:
            print(f"\n✅ 转换成功！")
            print(f"📁 文件保存位置: {result}")
            print(f"\n💡 提示:")
            print(f"   - 可以用文本编辑器打开查看Markdown文件")
            print(f"   - 图片链接已转换为Markdown格式")
            print(f"   - 输出目录: ./output/")
        else:
            print("\n❌ 转换失败，请检查URL是否正确")
            
    except Exception as e:
        print(f"\n❌ 转换出错: {e}")
        print("\n💡 可能的解决方案:")
        print("   - 检查网络连接")
        print("   - 确认URL格式正确")
        print("   - 某些网站可能有反爬虫机制")

def show_features():
    """显示功能特点"""
    print("\n🎯 功能特点:")
    print("   ✅ 支持微信公众号文章")
    print("   ✅ 自动提取正文内容")
    print("   ✅ 图片转换为Markdown链接")
    print("   ✅ 智能清理无关内容")
    print("   ✅ 支持批量处理")
    print("   ✅ 可配置内容选择器")
    
    print("\n📚 更多用法:")
    print("   python webpage_to_markdown.py <URL>           # 转换单个网页")
    print("   python webpage_to_markdown.py -f urls.txt     # 批量转换")
    print("   python webpage_to_markdown.py -c config.json  # 使用配置文件")
    print("   python example.py                             # 运行示例")
    print("   python test_websites.py                       # 测试不同网站")

if __name__ == "__main__":
    show_features()
    print("\n" + "=" * 50)
    quick_demo()
    print("\n" + "=" * 50)
    print("🎉 演示完成！感谢使用网页转Markdown工具！")
