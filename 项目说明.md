# 网页转Markdown工具 - 项目说明

## 项目概述

这是一个功能强大的Python脚本，可以读取网页中的正文内容并输出为Markdown文档。特别针对微信公众号等网站进行了优化，能够智能提取主要内容并将图片转换为Markdown链接格式。

## 核心功能

### ✅ 已实现功能

1. **智能内容提取**
   - 自动识别网页主要内容区域
   - 特别优化微信公众号文章提取
   - 支持多种网站结构

2. **图片处理**
   - 自动提取图片链接（支持懒加载）
   - 转换为标准Markdown图片格式
   - 处理相对路径和绝对路径

3. **内容清理**
   - 移除导航、广告等无关内容
   - 清理多余的HTML属性
   - 保留文章结构和格式

4. **多种使用方式**
   - 命令行工具
   - Python模块导入
   - 批量处理
   - 配置文件支持

5. **错误处理**
   - 网络重试机制
   - 详细错误信息
   - 优雅降级处理

## 文件结构

```
WebPageSave/
├── webpage_to_markdown.py    # 主脚本文件
├── requirements.txt          # Python依赖
├── config.json              # 配置文件示例
├── README.md                # 使用说明
├── quick_start.py           # 快速开始脚本
├── example.py               # 使用示例
├── test_websites.py         # 测试脚本
├── urls.txt                 # URL列表示例
├── output/                  # 输出目录
│   └── *.md                # 生成的Markdown文件
└── 项目说明.md              # 本文件
```

## 技术特点

### 1. 智能内容选择器
```python
"content_selectors": [
    ".rich_media_content",    # 微信公众号
    "article",               # 标准文章标签
    ".article-content",      # 常见文章类名
    ".content",              # 通用内容类名
    "main",                  # 主要内容区域
    "#content"               # 内容ID
]
```

### 2. 图片懒加载处理
```python
# 优先使用data-src（懒加载），然后是src
src = img.get('data-src', '') or img.get('src', '')
```

### 3. 网络重试机制
```python
for attempt in range(self.config['retry_times']):
    try:
        response = self.session.get(url, timeout=self.config['timeout'])
        # 处理响应
        break
    except Exception as e:
        if attempt < self.config['retry_times'] - 1:
            time.sleep(self.config['delay'])
```

## 配置说明

### 主要配置项

- `output_dir`: 输出目录
- `timeout`: 请求超时时间
- `retry_times`: 重试次数
- `remove_tags`: 要移除的HTML标签
- `content_selectors`: 内容选择器列表

### 示例配置
```json
{
  "output_dir": "./output",
  "timeout": 30,
  "retry_times": 3,
  "content_selectors": [
    ".rich_media_content",
    "article",
    ".content"
  ]
}
```

## 使用场景

1. **技术文档整理**
   - 保存技术博客文章
   - 整理学习资料
   - 创建知识库

2. **内容备份**
   - 备份重要文章
   - 离线阅读准备
   - 格式统一化

3. **批量处理**
   - 批量转换文章列表
   - 自动化内容处理
   - 定期备份任务

## 测试结果

### 微信公众号测试
- ✅ 标题提取: 正确
- ✅ 正文内容: 完整
- ✅ 图片链接: 正确（支持懒加载）
- ✅ 格式保持: 良好
- ✅ 转换速度: ~1秒

### 支持的网站类型
- ✅ 微信公众号文章
- ✅ 技术博客
- ✅ 新闻网站
- ✅ 个人博客
- ⚠️ 动态加载网站（部分支持）

## 扩展建议

### 可能的改进方向

1. **增强功能**
   - 支持JavaScript渲染
   - 图片本地下载
   - PDF导出功能
   - 自定义模板

2. **性能优化**
   - 并发处理
   - 缓存机制
   - 增量更新

3. **用户体验**
   - GUI界面
   - 浏览器插件
   - 在线服务

## 注意事项

1. **网站限制**
   - 某些网站有反爬虫机制
   - 需要登录的内容无法获取
   - 动态加载内容可能不完整

2. **图片链接**
   - 图片可能存在防盗链
   - 建议下载到本地使用
   - 某些图片可能失效

3. **法律合规**
   - 遵守网站使用条款
   - 尊重版权
   - 仅用于个人学习和备份

## 总结

这个网页转Markdown工具成功实现了您的需求：

1. ✅ 读取网页正文内容
2. ✅ 输出为Markdown格式
3. ✅ 图片转换为链接
4. ✅ 可配置网址
5. ✅ 特别优化微信公众号

工具具有良好的扩展性和易用性，可以满足日常的网页内容提取和转换需求。
